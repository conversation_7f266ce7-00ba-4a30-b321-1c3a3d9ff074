package com.ruoyi.evaluate.evaluateDataB.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateDataB.domain.ServerRoom;
import com.ruoyi.evaluate.evaluateDataB.service.IServerRoomService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * B4 物理机房Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/evaluateDataB/room")
@Api(value = "B4 物理机房控制器", tags = {"B4 物理机房管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ServerRoomController extends BaseController
{
    private final IServerRoomService serverRoomService;

    /**
     * 查询B4 物理机房列表
     */
    @ApiOperation("查询B4 物理机房列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:room:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServerRoom serverRoom) {
        startPage();
        List<ServerRoom> list = serverRoomService.list(new QueryWrapper<ServerRoom>(serverRoom));
        return getDataTable(list);
    }

    /**
     * 获取B4 物理机房详细信息
     */
    @ApiOperation("获取B4 物理机房详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:room:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(serverRoomService.getById(id));
    }

    /**
     * 新增B4 物理机房
     */
    @ApiOperation("新增B4 物理机房")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:room:add')")
    @Log(title = "B4 物理机房", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServerRoom serverRoom) {
        return toAjax(serverRoomService.save(serverRoom));
    }

    /**
     * 修改B4 物理机房
     */
    @ApiOperation("修改B4 物理机房")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:room:edit')")
    @Log(title = "B4 物理机房", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServerRoom serverRoom) {
        return toAjax(serverRoomService.updateById(serverRoom));
    }

    /**
     * 删除B4 物理机房
     */
    @ApiOperation("删除B4 物理机房")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:room:remove')")
    @Log(title = "B4 物理机房", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(serverRoomService.removeByIds(Arrays.asList(ids)));
    }
}