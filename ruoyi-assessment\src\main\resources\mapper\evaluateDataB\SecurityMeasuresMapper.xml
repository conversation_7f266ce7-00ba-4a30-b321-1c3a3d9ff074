<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluateDataB.mapper.SecurityMeasuresMapper">
    
    <resultMap type="SecurityMeasures" id="SecurityMeasuresResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="systemId"    column="system_id"    />
        <result property="dataId"    column="data_id"    />
        <result property="measure"    column="measure"    />
        <result property="result"    column="result"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="uuid"    column="uuid"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectSecurityMeasuresVo">
        select id, org_id, plan_id, system_id, data_id, measure, result, create_time, update_time, uuid, del_flag from dsa_security_measures
    </sql>

    <select id="selectSecurityMeasuresList" parameterType="SecurityMeasures" resultMap="SecurityMeasuresResult">
        <include refid="selectSecurityMeasuresVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="systemId != null "> and system_id = #{systemId}</if>
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="measure != null  and measure != ''"> and measure = #{measure}</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="uuid != null  and uuid != ''"> and uuid = #{uuid}</if>
        </where>
    </select>
    
    <select id="selectSecurityMeasuresById" parameterType="Long" resultMap="SecurityMeasuresResult">
        <include refid="selectSecurityMeasuresVo"/>
        where id = #{id}
    </select>

    <insert id="insertSecurityMeasures" parameterType="SecurityMeasures" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_security_measures
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="systemId != null">system_id,</if>
            <if test="dataId != null">data_id,</if>
            <if test="measure != null">measure,</if>
            <if test="result != null">result,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="uuid != null">uuid,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="systemId != null">#{systemId},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="measure != null">#{measure},</if>
            <if test="result != null">#{result},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="uuid != null">#{uuid},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateSecurityMeasures" parameterType="SecurityMeasures">
        update dsa_security_measures
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="result != null">result = #{result},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="uuid != null">uuid = #{uuid},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSecurityMeasuresById" parameterType="Long">
        delete from dsa_security_measures where id = #{id}
    </delete>

    <delete id="deleteSecurityMeasuresByIds" parameterType="String">
        delete from dsa_security_measures where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>