package com.ruoyi.evaluate.evaluateDataB.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateDataB.domain.NetworkDevice;
import com.ruoyi.evaluate.evaluateDataB.service.INetworkDeviceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * B5 网络设备Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/evaluateDataB/networkDevice")
@Api(value = "B5 网络设备控制器", tags = {"B5 网络设备管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class NetworkDeviceController extends BaseController
{
    private final INetworkDeviceService networkDeviceService;

    /**
     * 查询B5 网络设备列表
     */
    @ApiOperation("查询B5 网络设备列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:networkDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(NetworkDevice networkDevice) {
        startPage();
        List<NetworkDevice> list = networkDeviceService.list(new QueryWrapper<NetworkDevice>(networkDevice));
        return getDataTable(list);
    }

    /**
     * 导出B5 网络设备列表
     */
    @ApiOperation("导出B5 网络设备列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:networkDevice:export')")
    @Log(title = "B5 网络设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,NetworkDevice networkDevice) {
        List<NetworkDevice> list = networkDeviceService.list(new QueryWrapper<NetworkDevice>(networkDevice));
        ExcelUtil<NetworkDevice> util = new ExcelUtil<NetworkDevice>(NetworkDevice.class);
        util.exportExcel(response,list, "B5 网络设备数据");
    }

    /**
     * 获取B5 网络设备详细信息
     */
    @ApiOperation("获取B5 网络设备详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:networkDevice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(networkDeviceService.getById(id));
    }

    /**
     * 新增B5 网络设备
     */
    @ApiOperation("新增B5 网络设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:networkDevice:add')")
    @Log(title = "B5 网络设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NetworkDevice networkDevice) {
        return toAjax(networkDeviceService.save(networkDevice));
    }

    /**
     * 修改B5 网络设备
     */
    @ApiOperation("修改B5 网络设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:networkDevice:edit')")
    @Log(title = "B5 网络设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NetworkDevice networkDevice) {
        return toAjax(networkDeviceService.updateById(networkDevice));
    }

    /**
     * 删除B5 网络设备
     */
    @ApiOperation("删除B5 网络设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:networkDevice:remove')")
    @Log(title = "B5 网络设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(networkDeviceService.removeByIds(Arrays.asList(ids)));
    }
}