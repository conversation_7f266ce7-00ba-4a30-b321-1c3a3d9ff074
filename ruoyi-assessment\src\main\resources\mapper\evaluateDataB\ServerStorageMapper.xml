<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluateDataB.mapper.ServerStorageMapper">
    
    <resultMap type="ServerStorage" id="ServerStorageResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="systemId"    column="system_id"    />
        <result property="dataId"    column="data_id"    />
        <result property="name"    column="name"    />
        <result property="systemName"    column="system_name"    />
        <result property="isVirtual"    column="is_virtual"    />
        <result property="osVersion"    column="os_version"    />
        <result property="dbVersion"    column="db_version"    />
        <result property="middwareVersion"    column="middware_version"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="uuid"    column="uuid"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectServerStorageVo">
        select id, org_id, plan_id, system_id, data_id, name, system_name, is_virtual, os_version, db_version, middware_version, create_time, update_time, uuid, del_flag from dsa_server_storage
    </sql>

    <select id="selectServerStorageList" parameterType="ServerStorage" resultMap="ServerStorageResult">
        <include refid="selectServerStorageVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="systemId != null "> and system_id = #{systemId}</if>
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="isVirtual != null  and isVirtual != ''"> and is_virtual = #{isVirtual}</if>
            <if test="osVersion != null  and osVersion != ''"> and os_version = #{osVersion}</if>
            <if test="dbVersion != null  and dbVersion != ''"> and db_version = #{dbVersion}</if>
            <if test="middwareVersion != null  and middwareVersion != ''"> and middware_version = #{middwareVersion}</if>
            <if test="uuid != null  and uuid != ''"> and uuid = #{uuid}</if>
        </where>
    </select>
    
    <select id="selectServerStorageById" parameterType="Long" resultMap="ServerStorageResult">
        <include refid="selectServerStorageVo"/>
        where id = #{id}
    </select>

    <insert id="insertServerStorage" parameterType="ServerStorage" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_server_storage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="systemId != null">system_id,</if>
            <if test="dataId != null">data_id,</if>
            <if test="name != null">name,</if>
            <if test="systemName != null">system_name,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="osVersion != null">os_version,</if>
            <if test="dbVersion != null">db_version,</if>
            <if test="middwareVersion != null">middware_version,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="uuid != null">uuid,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="systemId != null">#{systemId},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="name != null">#{name},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="osVersion != null">#{osVersion},</if>
            <if test="dbVersion != null">#{dbVersion},</if>
            <if test="middwareVersion != null">#{middwareVersion},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="uuid != null">#{uuid},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateServerStorage" parameterType="ServerStorage">
        update dsa_server_storage
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="osVersion != null">os_version = #{osVersion},</if>
            <if test="dbVersion != null">db_version = #{dbVersion},</if>
            <if test="middwareVersion != null">middware_version = #{middwareVersion},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="uuid != null">uuid = #{uuid},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServerStorageById" parameterType="Long">
        delete from dsa_server_storage where id = #{id}
    </delete>

    <delete id="deleteServerStorageByIds" parameterType="String">
        delete from dsa_server_storage where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>