package com.ruoyi.evaluate.evaluateDataB.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * B2 部门及人员信息对象 dsa_biz_data_member
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_biz_data_member")
public class BizDataMember extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(value = "id" , type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 所属单位ID */
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 评估计划ID */
    @Excel(name = "评估计划ID")
    @TableField(value = "plan_id")
    private Long planId;

    /** 业务系统id */
    @Excel(name = "业务系统id")
    @TableField(value = "biz_system_id")
    private Long bizSystemId;

    /** 数据项id */
    @Excel(name = "数据项id")
    @TableField(value = "biz_data_id")
    private Long bizDataId;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    @TableField(value = "name")
    private String name;

    /** 岗位或角色名称 */
    @Excel(name = "岗位或角色名称")
    @TableField(value = "job")
    private String job;

    /** 岗位职责 */
    @Excel(name = "岗位职责")
    @TableField(value = "duty")
    private String duty;

    /** 所属部门 */
    @Excel(name = "所属部门")
    @TableField(value = "dept")
    private String dept;

    /** 涉及的数据处理活动 */
    @Excel(name = "涉及的数据处理活动")
    @TableField(value = "processing_activity")
    private String processingActivity;

    /** 涉及的数据处理活动 */
    @Excel(name = "涉及的数据处理活动")
    @TableField(value = "data_process_activity")
    private String dataProcessActivity;

    /** 是否专职 */
    @Excel(name = "是否专职")
    @TableField(value = "full_time")
    private String fullTime;

    /** 国籍 */
    @Excel(name = "国籍")
    @TableField(value = "nationality")
    private String nationality;


    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态，1-正常 0-禁用")
    @TableField(value = "status")
    private Integer status;






}