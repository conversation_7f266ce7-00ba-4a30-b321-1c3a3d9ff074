package com.ruoyi.evaluate.evaluateDataB.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateDataB.domain.ServerStorage;
import com.ruoyi.evaluate.evaluateDataB.service.IServerStorageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * B7 服务器|存储设备Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/evaluateDataB/storage")
@Api(value = "B7 服务器|存储设备控制器", tags = {"B7 服务器|存储设备管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ServerStorageController extends BaseController
{
    private final IServerStorageService serverStorageService;

    /**
     * 查询B7 服务器|存储设备列表
     */
    @ApiOperation("查询B7 服务器|存储设备列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:storage:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServerStorage serverStorage) {
        startPage();
        List<ServerStorage> list = serverStorageService.list(new QueryWrapper<ServerStorage>(serverStorage));
        return getDataTable(list);
    }

    /**
     * 获取B7 服务器|存储设备详细信息
     */
    @ApiOperation("获取B7 服务器|存储设备详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:storage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(serverStorageService.getById(id));
    }

    /**
     * 新增B7 服务器|存储设备
     */
    @ApiOperation("新增B7 服务器|存储设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:storage:add')")
    @Log(title = "B7 服务器|存储设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServerStorage serverStorage) {
        return toAjax(serverStorageService.save(serverStorage));
    }

    /**
     * 修改B7 服务器|存储设备
     */
    @ApiOperation("修改B7 服务器|存储设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:storage:edit')")
    @Log(title = "B7 服务器|存储设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServerStorage serverStorage) {
        return toAjax(serverStorageService.updateById(serverStorage));
    }

    /**
     * 删除B7 服务器|存储设备
     */
    @ApiOperation("删除B7 服务器|存储设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:storage:remove')")
    @Log(title = "B7 服务器|存储设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(serverStorageService.removeByIds(Arrays.asList(ids)));
    }
}