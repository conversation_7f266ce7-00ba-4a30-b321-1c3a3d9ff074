package com.ruoyi.evaluate.evaluatePlan.service;

import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.SaveStepRequest;
import com.ruoyi.evaluate.evaluatePlan.dto.DraftStepRequest;

import java.util.List;
import java.util.Map;

/**
 * 评估计划暂存服务接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IEvaluatePlanDraftService {

    /**
     * 暂存评估计划步骤数据
     *
     * @param userId 用户ID
     * @param request 暂存请求参数
     * @return 暂存结果
     */
    boolean saveDraft(Long userId, SaveStepRequest request);

    /**
     * 获取评估计划步骤暂存数据
     *
     * @param userId 用户ID
     * @param request 步骤请求参数
     * @return 暂存数据结果
     */
    StepDataResponse getDraft(Long userId, DraftStepRequest request);
}
