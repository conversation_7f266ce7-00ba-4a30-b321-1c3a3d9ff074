package com.ruoyi.evaluate.evaluateDataB.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * B9 已有安全保障措施对象 dsa_security_measures
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_security_measures")
public class SecurityMeasures extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(value = "id" , type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 所属单位ID */
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 评估计划ID */
    @Excel(name = "评估计划ID")
    @TableField(value = "plan_id")
    private Long planId;

    /** 系统ID */
    @Excel(name = "系统ID")
    @TableField(value = "system_id")
    private Long systemId;

    /** 数据项ID */
    @Excel(name = "数据项ID")
    @TableField(value = "data_id")
    private Long dataId;

    /** 安全措施 */
    @Excel(name = "安全措施")
    @TableField(value = "measure")
    private String measure;

    /** 评估结果,是 否 */
    @Excel(name = "评估结果,是 否")
    @TableField(value = "result")
    private String result;



    /** uuid */
    @Excel(name = "uuid")
    @TableField(value = "uuid")
    private String uuid;


}