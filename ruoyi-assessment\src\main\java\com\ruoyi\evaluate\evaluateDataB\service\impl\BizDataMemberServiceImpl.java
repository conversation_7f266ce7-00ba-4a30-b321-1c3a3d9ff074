package com.ruoyi.evaluate.evaluateDataB.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateDataB.mapper.BizDataMemberMapper;
import com.ruoyi.evaluate.evaluateDataB.domain.BizDataMember;
import com.ruoyi.evaluate.evaluateDataB.service.IBizDataMemberService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * B2 部门及人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class BizDataMemberServiceImpl extends ServiceImpl<BizDataMemberMapper, BizDataMember> implements IBizDataMemberService {

}
