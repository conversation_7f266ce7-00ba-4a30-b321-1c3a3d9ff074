package com.ruoyi.evaluate.evaluateDataB.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateDataB.domain.SecurityDevice;
import com.ruoyi.evaluate.evaluateDataB.service.ISecurityDeviceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * B6 安全设备Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/evaluateDataB/securityDevice")
@Api(value = "B6 安全设备控制器", tags = {"B6 安全设备管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class SecurityDeviceController extends BaseController
{
    private final ISecurityDeviceService securityDeviceService;

    /**
     * 查询B6 安全设备列表
     */
    @ApiOperation("查询B6 安全设备列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:securityDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(SecurityDevice securityDevice) {
        startPage();
        List<SecurityDevice> list = securityDeviceService.list(new QueryWrapper<SecurityDevice>(securityDevice));
        return getDataTable(list);
    }

    /**
     * 导出B6 安全设备列表
     */
    @ApiOperation("导出B6 安全设备列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:securityDevice:export')")
    @Log(title = "B6 安全设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,SecurityDevice securityDevice) {
        List<SecurityDevice> list = securityDeviceService.list(new QueryWrapper<SecurityDevice>(securityDevice));
        ExcelUtil<SecurityDevice> util = new ExcelUtil<SecurityDevice>(SecurityDevice.class);
        util.exportExcel(response,list, "B6 安全设备数据");
    }

    /**
     * 获取B6 安全设备详细信息
     */
    @ApiOperation("获取B6 安全设备详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:securityDevice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityDeviceService.getById(id));
    }

    /**
     * 新增B6 安全设备
     */
    @ApiOperation("新增B6 安全设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:securityDevice:add')")
    @Log(title = "B6 安全设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityDevice securityDevice) {
        return toAjax(securityDeviceService.save(securityDevice));
    }

    /**
     * 修改B6 安全设备
     */
    @ApiOperation("修改B6 安全设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:securityDevice:edit')")
    @Log(title = "B6 安全设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityDevice securityDevice) {
        return toAjax(securityDeviceService.updateById(securityDevice));
    }

    /**
     * 删除B6 安全设备
     */
    @ApiOperation("删除B6 安全设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:securityDevice:remove')")
    @Log(title = "B6 安全设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityDeviceService.removeByIds(Arrays.asList(ids)));
    }
}