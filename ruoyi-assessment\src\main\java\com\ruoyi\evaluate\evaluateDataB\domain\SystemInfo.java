package com.ruoyi.evaluate.evaluateDataB.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * B3 信息系统情况对象 dsa_system_info
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_system_info")
public class SystemInfo extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(value = "id" , type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 所属单位ID */
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 评估计划ID */
    @Excel(name = "评估计划ID")
    @TableField(value = "plan_id")
    private Long planId;

    /** 系统ID */
    @Excel(name = "系统ID")
    @TableField(value = "system_id")
    private Long systemId;

    /** 数据项ID */
    @Excel(name = "数据项ID")
    @TableField(value = "data_id")
    private Long dataId;

    /** 信息系统名称 */
    @Excel(name = "信息系统名称")
    @TableField(value = "system_name")
    private String systemName;

    /** 所在设备名称 */
    @Excel(name = "所在设备名称")
    @TableField(value = "device_name")
    private String deviceName;

    /** 版本 */
    @Excel(name = "版本")
    @TableField(value = "version")
    private String version;

    /** 主要功能 */
    @Excel(name = "主要功能")
    @TableField(value = "main_function")
    private String mainFunction;

    /** 网络安全等级保护测评情况 */
    @Excel(name = "网络安全等级保护测评情况")
    @TableField(value = "security_evaluation")
    private String securityEvaluation;



    /** uuid */
    @Excel(name = "uuid")
    @TableField(value = "uuid")
    private String uuid;


}