package com.ruoyi.evaluate.evaluateDataB.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateDataB.domain.SystemInfo;
import com.ruoyi.evaluate.evaluateDataB.service.ISystemInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * B3 信息系统情况Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/evaluateDataB/info")
@Api(value = "B3 信息系统情况控制器", tags = {"B3 信息系统情况管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class SystemInfoController extends BaseController
{
    private final ISystemInfoService systemInfoService;

    /**
     * 查询B3 信息系统情况列表
     */
    @ApiOperation("查询B3 信息系统情况列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(SystemInfo systemInfo) {
        startPage();
        List<SystemInfo> list = systemInfoService.list(new QueryWrapper<SystemInfo>(systemInfo));
        return getDataTable(list);
    }

    /**
     * 获取B3 信息系统情况详细信息
     */
    @ApiOperation("获取B3 信息系统情况详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(systemInfoService.getById(id));
    }

    /**
     * 新增B3 信息系统情况
     */
    @ApiOperation("新增B3 信息系统情况")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:info:add')")
    @Log(title = "B3 信息系统情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SystemInfo systemInfo) {
        return toAjax(systemInfoService.save(systemInfo));
    }

    /**
     * 修改B3 信息系统情况
     */
    @ApiOperation("修改B3 信息系统情况")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:info:edit')")
    @Log(title = "B3 信息系统情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SystemInfo systemInfo) {
        return toAjax(systemInfoService.updateById(systemInfo));
    }

    /**
     * 删除B3 信息系统情况
     */
    @ApiOperation("删除B3 信息系统情况")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:info:remove')")
    @Log(title = "B3 信息系统情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(systemInfoService.removeByIds(Arrays.asList(ids)));
    }
}