package com.ruoyi.evaluate.evaluateDataB.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateDataB.mapper.SecurityDeviceMapper;
import com.ruoyi.evaluate.evaluateDataB.domain.SecurityDevice;
import com.ruoyi.evaluate.evaluateDataB.service.ISecurityDeviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * B6 安全设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class SecurityDeviceServiceImpl extends ServiceImpl<SecurityDeviceMapper, SecurityDevice> implements ISecurityDeviceService {

}
