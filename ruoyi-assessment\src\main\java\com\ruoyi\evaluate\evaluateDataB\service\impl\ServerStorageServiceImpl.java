package com.ruoyi.evaluate.evaluateDataB.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateDataB.mapper.ServerStorageMapper;
import com.ruoyi.evaluate.evaluateDataB.domain.ServerStorage;
import com.ruoyi.evaluate.evaluateDataB.service.IServerStorageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * B7 服务器|存储设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class ServerStorageServiceImpl extends ServiceImpl<ServerStorageMapper, ServerStorage> implements IServerStorageService {

}
