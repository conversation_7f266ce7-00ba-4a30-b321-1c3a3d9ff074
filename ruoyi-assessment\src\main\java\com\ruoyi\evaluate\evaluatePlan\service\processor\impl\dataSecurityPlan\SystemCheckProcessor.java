package com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurityPlan;

import com.ruoyi.evaluate.evaluatePlan.domain.CheckList;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.service.processor.AbstractStepDataProcessor;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import com.ruoyi.process.domain.ProcessStepInstance;
import org.springframework.stereotype.Component;

/**
 * 系统检测
 *
 * <AUTHOR>
 * @date 2025/7/30 17:38
 */
@Component
public class SystemCheckProcessor extends AbstractStepDataProcessor {

    @Override
    public String getEvaluateType() {
        return "data_security_plan";
    }

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    @Override
    public String getDraftDataType() {
        return DraftDataTypeEnum.LIST.getCode();
    }

    /**
     * 获取支持的步骤编码
     *
     * @return 步骤编码
     */
    @Override
    public String getStepCode() {
        return "system_check";
    }

    /**
     * 获取步骤名称（由子类实现）
     *
     * @return 步骤名称
     */
    @Override
    protected String getStepName() {
        return "系统校验";
    }

    public CheckList getCheckList() {
        CheckList checkList = new CheckList();

        // 创建被检查单位信息
        CheckList.UnitInfo unitInfo = new CheckList.UnitInfo()
                .setAoBasicInfo("AO基本信息检查项")
                .setA1DataProcessorBasicInfo("数据处理者基本情况检查项")
                .setA2DepartmentInfo("部门信息检查项")
                .setA3PersonnelInfo("人员信息检查项")
                .setA4SecurityManagementSystem("安全管理制度检查项")
                .setA5SecurityManagementOrganization("安全管理机构检查项");

        // 创建评估单位信息
        CheckList.EvaluationUnit evaluationUnit = new CheckList.EvaluationUnit()
                .setEvaluationUnitName("评估单位检查项")
                .setProjectManagementGroup("项目管理组检查项")
                .setTechnicalSupportGroup("技术支持组检查项")
                .setQualityManagementGroup("质量管理组检查项")
                .setConfigurationManagementGroup("配置管理组检查项");

        // 设置到检查清单中
        checkList.setUnitInfo(unitInfo)
                .setEvaluationUnit(evaluationUnit);

        return checkList;
    }

    /**
     * 保存步骤数据
     *
     * @param planTask          评估计划任务
     * @param stepCode          步骤编码
     * @param stepData          步骤数据
     * @param processInstanceId 流程实例ID
     * @return 保存是否成功
     */
    @Override
    public boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId) {
        return true;
    }

    /**
     * 处理具体步骤数据（由子类实现）
     *
     * @param planTask          评估计划任务
     * @param stepInstance      步骤实例
     * @param processInstanceId 流程实例ID
     * @param response          步骤数据响应对象，子类在此基础上添加扩展数据
     */
    @Override
    protected void getSpecificStepData(EvaluatePlanTask planTask, ProcessStepInstance stepInstance, Long processInstanceId, StepDataResponse response) {
        // 3. 添加特定的扩展数据
        response.addStepData(getDraftDataType(), getCheckList());
    }

    @Override
    protected void getListDraftData(Long userId, EvaluatePlanTask planTask, String stepCode, StepDataResponse response) {
        response.addStepData(getDraftDataType(), getCheckList());
    }
}
