package com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurityPlan;

import com.ruoyi.evaluate.evaluatePlan.domain.CheckCategory;
import com.ruoyi.evaluate.evaluatePlan.domain.CheckList;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.service.processor.AbstractStepDataProcessor;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import com.ruoyi.process.domain.ProcessStepInstance;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统检测
 *
 * <AUTHOR>
 * @date 2025/7/30 17:38
 */
@Component
public class SystemCheckProcessor extends AbstractStepDataProcessor {

    @Override
    public String getEvaluateType() {
        return "data_security_plan";
    }

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    @Override
    public String getDraftDataType() {
        return DraftDataTypeEnum.LIST.getCode();
    }

    /**
     * 获取支持的步骤编码
     *
     * @return 步骤编码
     */
    @Override
    public String getStepCode() {
        return "system_check";
    }

    /**
     * 获取步骤名称（由子类实现）
     *
     * @return 步骤名称
     */
    @Override
    protected String getStepName() {
        return "系统校验";
    }

    public List<CheckCategory> getCheckList() {
        List<CheckCategory> checkCategories = new ArrayList<>();

        // 第一大类：被检查单位信息 (占比16%)
        CheckCategory unitInfoCategory = new CheckCategory();
        unitInfoCategory.setCategoryName("被检查单位信息");

        List<CheckList.CheckItem> unitInfoItems = new ArrayList<>();
        unitInfoItems.add(new CheckList.CheckItem("AO", "AO基本信息", "AO基本信息检查项", true, "检查通过"));
        unitInfoItems.add(new CheckList.CheckItem("A1", "数据处理者基本情况", "数据处理者基本情况检查项", false, "缺少相关文档"));
        unitInfoItems.add(new CheckList.CheckItem("A2", "部门信息", "部门信息检查项", true, "信息完整"));
        unitInfoItems.add(new CheckList.CheckItem("A3", "人员信息", "人员信息检查项", false, "待检查"));
        unitInfoItems.add(new CheckList.CheckItem("A4", "安全管理制度", "安全管理制度检查项", true, "制度完善"));
        unitInfoItems.add(new CheckList.CheckItem("A5", "安全管理机构", "安全管理机构检查项", false, "机构设置不完整"));

        unitInfoCategory.setCheckItems(unitInfoItems);
        checkCategories.add(unitInfoCategory);

        // 第二大类：评估单位 (占比20%)
        CheckCategory evaluationUnitCategory = new CheckCategory();
        evaluationUnitCategory.setCategoryName("评估单位");

        List<CheckList.CheckItem> evaluationUnitItems = new ArrayList<>();
        evaluationUnitItems.add(new CheckList.CheckItem("EU", "评估单位", "评估单位检查项", true, "资质齐全"));
        evaluationUnitItems.add(new CheckList.CheckItem("PMG", "项目管理组", "项目管理组检查项", true, "管理规范"));
        evaluationUnitItems.add(new CheckList.CheckItem("TSG", "技术支持组", "技术支持组检查项", false, "技术能力待提升"));
        evaluationUnitItems.add(new CheckList.CheckItem("QMG", "质量管理组", "质量管理组检查项", true, "质量控制到位"));
        evaluationUnitItems.add(new CheckList.CheckItem("CMG", "配置管理组", "配置管理组检查项", false, "待检查"));

        evaluationUnitCategory.setCheckItems(evaluationUnitItems);
        checkCategories.add(evaluationUnitCategory);

        return checkCategories;
    }


    /**
     * 保存步骤数据
     *
     * @param planTask          评估计划任务
     * @param stepCode          步骤编码
     * @param stepData          步骤数据
     * @param processInstanceId 流程实例ID
     * @return 保存是否成功
     */
    @Override
    public boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId) {
        return true;
    }

    /**
     * 处理具体步骤数据（由子类实现）
     *
     * @param planTask          评估计划任务
     * @param stepInstance      步骤实例
     * @param processInstanceId 流程实例ID
     * @param response          步骤数据响应对象，子类在此基础上添加扩展数据
     */
    @Override
    protected void getSpecificStepData(EvaluatePlanTask planTask, ProcessStepInstance stepInstance, Long processInstanceId, StepDataResponse response) {
        // 3. 添加特定的扩展数据
        response.addStepData(getDraftDataType(), getCheckList());
    }

    @Override
    protected void getListDraftData(Long userId, EvaluatePlanTask planTask, String stepCode, StepDataResponse response) {
        response.addStepData(getDraftDataType(), getCheckList());
    }
}
