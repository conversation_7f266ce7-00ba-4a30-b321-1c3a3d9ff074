<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluateDataB.mapper.NetworkDeviceMapper">
    
    <resultMap type="NetworkDevice" id="NetworkDeviceResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="systemId"    column="system_id"    />
        <result property="dataId"    column="data_id"    />
        <result property="name"    column="name"    />
        <result property="isVirtual"    column="is_virtual"    />
        <result property="version"    column="version"    />
        <result property="brand"    column="brand"    />
        <result property="purpose"    column="purpose"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="uuid"    column="uuid"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectNetworkDeviceVo">
        select id, org_id, plan_id, system_id, data_id, name, is_virtual, version, brand, purpose, create_time, update_time, uuid, del_flag from dsa_network_device
    </sql>

    <select id="selectNetworkDeviceList" parameterType="NetworkDevice" resultMap="NetworkDeviceResult">
        <include refid="selectNetworkDeviceVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="systemId != null "> and system_id = #{systemId}</if>
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="isVirtual != null  and isVirtual != ''"> and is_virtual = #{isVirtual}</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="brand != null  and brand != ''"> and brand = #{brand}</if>
            <if test="purpose != null  and purpose != ''"> and purpose = #{purpose}</if>
            <if test="uuid != null  and uuid != ''"> and uuid = #{uuid}</if>
        </where>
    </select>
    
    <select id="selectNetworkDeviceById" parameterType="Long" resultMap="NetworkDeviceResult">
        <include refid="selectNetworkDeviceVo"/>
        where id = #{id}
    </select>

    <insert id="insertNetworkDevice" parameterType="NetworkDevice" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_network_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="systemId != null">system_id,</if>
            <if test="dataId != null">data_id,</if>
            <if test="name != null">name,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="version != null">version,</if>
            <if test="brand != null">brand,</if>
            <if test="purpose != null">purpose,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="uuid != null">uuid,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="systemId != null">#{systemId},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="name != null">#{name},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="version != null">#{version},</if>
            <if test="brand != null">#{brand},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="uuid != null">#{uuid},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateNetworkDevice" parameterType="NetworkDevice">
        update dsa_network_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="version != null">version = #{version},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="uuid != null">uuid = #{uuid},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNetworkDeviceById" parameterType="Long">
        delete from dsa_network_device where id = #{id}
    </delete>

    <delete id="deleteNetworkDeviceByIds" parameterType="String">
        delete from dsa_network_device where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>