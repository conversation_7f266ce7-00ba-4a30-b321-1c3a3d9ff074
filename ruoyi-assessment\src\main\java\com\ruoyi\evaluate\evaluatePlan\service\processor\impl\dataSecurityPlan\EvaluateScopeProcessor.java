package com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurityPlan;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.evaluate.evaluateData.domain.EvaluatePlanEvaluateScope;
import com.ruoyi.evaluate.evaluateData.service.IEvaluatePlanEvaluateScopeService;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.service.processor.AbstractStepDataProcessor;
import com.ruoyi.process.domain.ProcessStepInstance;

import javax.annotation.Resource;

/**
 * 确定评估范围
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
public class EvaluateScopeProcessor extends AbstractStepDataProcessor {

    @Resource
    private IEvaluatePlanEvaluateScopeService evaluatePlanEvaluateScopeService;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public String getStepCode() {
        return "evaluate_scope";
    }

    @Override
    public String getEvaluateType() {
        return "data_security_plan";
    }

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    @Override
    public String getDraftDataType() {
        return DraftDataTypeEnum.FORM.getCode();
    }

    @Override
    protected String getStepName() {
        return "确定评估范围";
    }


    @Override
    public StepDataResponse getStepData(EvaluatePlanTask planTask, String stepCode, Long processInstanceId) {
        // 1. 根据stepCode和processInstanceId查找对应的stepInstance
        ProcessStepInstance stepInstance = findStepInstanceByCode(stepCode, processInstanceId);

        // 2. 使用实体类构建响应
        StepDataResponse response = StepDataConverter.buildBaseStepDataResponse(
                planTask, stepInstance, stepCode, getEvaluateType());

        // 3. 添加特定的扩展数据
        /*if (stepInstance != null) {
            response.setMessage("步骤实例已找到");
            // 将具体步骤数据作为扩展数据添加
            Map<String, Object> specificData = processSpecificStepData(planTask, stepInstance, processInstanceId);
            response.addExtensionData("data", specificData);
        } else {
            response.setMessage("未找到对应的步骤实例");
            response.addExtensionData("data", new HashMap<>());
        }*/

        getSpecificStepData(planTask, stepInstance, processInstanceId, response);

        // 4. 转换为Map格式（保持向后兼容）
        return response;
    }

    /**
     * 保存步骤数据
     *
     * @param planTask          评估计划任务
     * @param stepCode          步骤编码
     * @param stepData          步骤数据
     * @param processInstanceId 流程实例ID
     * @return 保存是否成功
     */
    @Override
    public boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId) {
        try {
            log.info("开始保存评估范围步骤数据，计划ID: {}, 步骤: {}, 数据类型: {}",
                    planTask.getId(), stepCode, stepData != null ? stepData.getClass().getSimpleName() : "null");

            // 1. 查询现有数据
            EvaluatePlanEvaluateScope queryCondition = new EvaluatePlanEvaluateScope();
            queryCondition.setPlanId(planTask.getId());
            List<EvaluatePlanEvaluateScope> existingList = evaluatePlanEvaluateScopeService.list(
                    new QueryWrapper<>(queryCondition));

            log.debug("查询到现有评估范围数据数量: {}", existingList != null ? existingList.size() : 0);

            // 2. 准备保存的实体对象
            EvaluatePlanEvaluateScope evaluatePlanEvaluateScope;
            boolean isUpdate = false;

            if (existingList != null && !existingList.isEmpty()) {
                // 更新现有记录
                evaluatePlanEvaluateScope = existingList.get(0);
                isUpdate = true;
                log.debug("更新现有记录，ID: {}", evaluatePlanEvaluateScope.getId());
            } else {
                // 创建新记录
                evaluatePlanEvaluateScope = new EvaluatePlanEvaluateScope();
                evaluatePlanEvaluateScope.setPlanId(planTask.getId());
                evaluatePlanEvaluateScope.setOrgId(planTask.getOrgId());
                log.debug("创建新记录");
            }

            // 3. 使用JSON转换进行数据映射
            if (stepData != null) {
                EvaluatePlanEvaluateScope convertedData = convertStepDataToEntity(stepData);
                if (convertedData != null) {
                    // 复制转换后的数据到目标实体，但保留关键字段
                    Long originalId = evaluatePlanEvaluateScope.getId();
                    Long originalPlanId = evaluatePlanEvaluateScope.getPlanId();
                    Long originalOrgId = evaluatePlanEvaluateScope.getOrgId();

                    // 使用BeanUtils复制转换后的数据
                    BeanUtils.copyProperties(convertedData, evaluatePlanEvaluateScope);

                    // 恢复关键字段，确保不被覆盖
                    if (originalId != null) {
                        evaluatePlanEvaluateScope.setId(originalId);
                    }
                    if (originalPlanId != null) {
                        evaluatePlanEvaluateScope.setPlanId(originalPlanId);
                    }
                    if (originalOrgId != null) {
                        evaluatePlanEvaluateScope.setOrgId(originalOrgId);
                    }

                    log.debug("JSON转换完成，设置字段: systemIds={}, dataItemIds={}, activityIds={}",
                            evaluatePlanEvaluateScope.getSystemIds(),
                            evaluatePlanEvaluateScope.getDataItemIds(),
                            evaluatePlanEvaluateScope.getActivityIds());
                }
            }

            // 4. 执行保存或更新操作
            boolean result;
            if (isUpdate) {
                if (evaluatePlanEvaluateScope.getId() == null) {
                    throw new ServiceException("更新操作时记录ID不能为空");
                }
                result = evaluatePlanEvaluateScopeService.updateById(evaluatePlanEvaluateScope);
                log.info("更新评估范围数据完成，结果: {}, ID: {}", result, evaluatePlanEvaluateScope.getId());
            } else {
                result = evaluatePlanEvaluateScopeService.save(evaluatePlanEvaluateScope);
                log.info("保存评估范围数据完成，结果: {}, 新ID: {}", result, evaluatePlanEvaluateScope.getId());
            }

            return result;

        } catch (Exception e) {
            log.error("保存评估范围步骤数据异常，计划ID: {}, 步骤: {}, 错误: {}",
                    planTask.getId(), stepCode, e.getMessage(), e);
            throw new ServiceException("保存评估范围数据失败: " + e.getMessage());
        }
    }

    /**
     * 将stepData转换为EvaluatePlanEvaluateScope实体对象
     * 使用JSON序列化/反序列化进行类型安全的转换
     *
     * @param stepData 步骤数据（通常是Map或其他对象）
     * @return 转换后的实体对象，转换失败时返回null
     */
    private EvaluatePlanEvaluateScope convertStepDataToEntity(Object stepData) {
        try {
            // 1. 将stepData转换为JSON字符串
            String jsonString = objectMapper.writeValueAsString(stepData);
            log.debug("stepData转换为JSON: {}", jsonString);

            // 2. 将JSON字符串转换为目标实体对象
            EvaluatePlanEvaluateScope entity = objectMapper.readValue(jsonString, EvaluatePlanEvaluateScope.class);
            log.debug("JSON转换为实体成功");

            return entity;

        } catch (Exception e) {
            log.warn("stepData JSON转换失败，将跳过数据设置，原因: {}, stepData类型: {}",
                    e.getMessage(), stepData != null ? stepData.getClass().getSimpleName() : "null");
            return null;
        }
    }

    @Override
    protected boolean saveFormDraftData(Long userId, Long planId, String stepCode, Object stepData) {
        Map<String, Object> result = new HashMap<>();
        result.put("planId", planId);

        return true;
    }

    /**
     * 处理具体步骤数据（由子类实现）
     *
     * @param planTask          评估计划任务
     * @param stepInstance      步骤实例
     * @param processInstanceId 流程实例ID
     * @param response          步骤数据响应对象，子类在此基础上添加扩展数据
     */
    @Override
    protected void getSpecificStepData(EvaluatePlanTask planTask, ProcessStepInstance stepInstance, Long processInstanceId, StepDataResponse response) {
        EvaluatePlanEvaluateScope evaluatePlanEvaluateScope = evaluatePlanEvaluateScopeService.getOne(new QueryWrapper<EvaluatePlanEvaluateScope>()
                .eq("plan_id", planTask.getId()));

        response.addStepData(getDraftDataType(), evaluatePlanEvaluateScope);
    }

    /**
     * 重写获取暂存数据方法，提供特定的评估范围暂存数据处理
     * 参考 getStepData 方法的实现风格
     * 结合getStepData方法的设计，移除userId参数，从SecurityUtils获取当前用户ID
     */
    @Override
    protected void getFormDraftData(Long userId, EvaluatePlanTask planTask, String stepCode, StepDataResponse response) {
        Long planId = planTask.getId();

        EvaluatePlanEvaluateScope evaluatePlanEvaluateScope = evaluatePlanEvaluateScopeService.getOne(new QueryWrapper<EvaluatePlanEvaluateScope>()
                .eq("plan_id", planId));

        response.addStepData(getDraftDataType(), evaluatePlanEvaluateScope);
    }


}
