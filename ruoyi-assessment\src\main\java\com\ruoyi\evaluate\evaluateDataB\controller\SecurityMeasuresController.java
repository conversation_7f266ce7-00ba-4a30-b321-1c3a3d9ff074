package com.ruoyi.evaluate.evaluateDataB.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateDataB.domain.SecurityMeasures;
import com.ruoyi.evaluate.evaluateDataB.service.ISecurityMeasuresService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * B9 已有安全保障措施Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/evaluateDataB/measures")
@Api(value = "B9 已有安全保障措施控制器", tags = {"B9 已有安全保障措施管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class SecurityMeasuresController extends BaseController
{
    private final ISecurityMeasuresService securityMeasuresService;

    /**
     * 查询B9 已有安全保障措施列表
     */
    @ApiOperation("查询B9 已有安全保障措施列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:measures:list')")
    @GetMapping("/list")
    public TableDataInfo list(SecurityMeasures securityMeasures) {
        startPage();
        List<SecurityMeasures> list = securityMeasuresService.list(new QueryWrapper<SecurityMeasures>(securityMeasures));
        return getDataTable(list);
    }

    /**
     * 获取B9 已有安全保障措施详细信息
     */
    @ApiOperation("获取B9 已有安全保障措施详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:measures:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(securityMeasuresService.getById(id));
    }

    /**
     * 新增B9 已有安全保障措施
     */
    @ApiOperation("新增B9 已有安全保障措施")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:measures:add')")
    @Log(title = "B9 已有安全保障措施", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SecurityMeasures securityMeasures) {
        return toAjax(securityMeasuresService.save(securityMeasures));
    }

    /**
     * 修改B9 已有安全保障措施
     */
    @ApiOperation("修改B9 已有安全保障措施")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:measures:edit')")
    @Log(title = "B9 已有安全保障措施", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SecurityMeasures securityMeasures) {
        return toAjax(securityMeasuresService.updateById(securityMeasures));
    }

    /**
     * 删除B9 已有安全保障措施
     */
    @ApiOperation("删除B9 已有安全保障措施")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:measures:remove')")
    @Log(title = "B9 已有安全保障措施", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(securityMeasuresService.removeByIds(Arrays.asList(ids)));
    }
}