package com.ruoyi.evaluate.evaluateDataB.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateDataB.mapper.ComputerDeviceMapper;
import com.ruoyi.evaluate.evaluateDataB.domain.ComputerDevice;
import com.ruoyi.evaluate.evaluateDataB.service.IComputerDeviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * B8 终端|现场设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class ComputerDeviceServiceImpl extends ServiceImpl<ComputerDeviceMapper, ComputerDevice> implements IComputerDeviceService {

}
