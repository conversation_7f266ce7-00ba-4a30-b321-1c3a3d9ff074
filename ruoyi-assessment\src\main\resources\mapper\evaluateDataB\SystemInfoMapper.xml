<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.evaluate.evaluateDataB.mapper.SystemInfoMapper">
    
    <resultMap type="SystemInfo" id="SystemInfoResult">
        <result property="id"    column="id"    />
        <result property="orgId"    column="org_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="systemId"    column="system_id"    />
        <result property="dataId"    column="data_id"    />
        <result property="systemName"    column="system_name"    />
        <result property="deviceName"    column="device_name"    />
        <result property="version"    column="version"    />
        <result property="mainFunction"    column="main_function"    />
        <result property="securityEvaluation"    column="security_evaluation"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="uuid"    column="uuid"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectSystemInfoVo">
        select id, org_id, plan_id, system_id, data_id, system_name, device_name, version, main_function, security_evaluation, create_time, update_time, uuid, del_flag from dsa_system_info
    </sql>

    <select id="selectSystemInfoList" parameterType="SystemInfo" resultMap="SystemInfoResult">
        <include refid="selectSystemInfoVo"/>
        <where>  
            <if test="orgId != null "> and org_id = #{orgId}</if>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="systemId != null "> and system_id = #{systemId}</if>
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="mainFunction != null  and mainFunction != ''"> and main_function = #{mainFunction}</if>
            <if test="securityEvaluation != null  and securityEvaluation != ''"> and security_evaluation = #{securityEvaluation}</if>
            <if test="uuid != null  and uuid != ''"> and uuid = #{uuid}</if>
        </where>
    </select>
    
    <select id="selectSystemInfoById" parameterType="Long" resultMap="SystemInfoResult">
        <include refid="selectSystemInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSystemInfo" parameterType="SystemInfo" useGeneratedKeys="true" keyProperty="id">
        insert into dsa_system_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgId != null">org_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="systemId != null">system_id,</if>
            <if test="dataId != null">data_id,</if>
            <if test="systemName != null">system_name,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="version != null">version,</if>
            <if test="mainFunction != null">main_function,</if>
            <if test="securityEvaluation != null">security_evaluation,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="uuid != null">uuid,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgId != null">#{orgId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="systemId != null">#{systemId},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="version != null">#{version},</if>
            <if test="mainFunction != null">#{mainFunction},</if>
            <if test="securityEvaluation != null">#{securityEvaluation},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="uuid != null">#{uuid},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateSystemInfo" parameterType="SystemInfo">
        update dsa_system_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="version != null">version = #{version},</if>
            <if test="mainFunction != null">main_function = #{mainFunction},</if>
            <if test="securityEvaluation != null">security_evaluation = #{securityEvaluation},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="uuid != null">uuid = #{uuid},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSystemInfoById" parameterType="Long">
        delete from dsa_system_info where id = #{id}
    </delete>

    <delete id="deleteSystemInfoByIds" parameterType="String">
        delete from dsa_system_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>