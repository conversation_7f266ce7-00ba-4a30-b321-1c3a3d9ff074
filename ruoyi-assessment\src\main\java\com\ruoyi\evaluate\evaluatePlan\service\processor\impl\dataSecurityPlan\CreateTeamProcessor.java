package com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurityPlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.service.IDraftStorageService;
import com.ruoyi.evaluate.evaluateData.domain.EvaluatePlanTeamMember;
import com.ruoyi.evaluate.evaluateData.service.IEvaluatePlanTeamMemberService;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.service.processor.AbstractStepDataProcessor;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import com.ruoyi.process.domain.ProcessStepInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 组建评估团队
 *
 * <AUTHOR>
 * @date 2025/7/30 17:38
 */
@Slf4j
@Component
public class CreateTeamProcessor extends AbstractStepDataProcessor {

    @Autowired
    private IEvaluatePlanTeamMemberService evaluatePlanTeamMemberService;

    @Override
    public String getEvaluateType() {
        return "data_security_plan";
    }

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    @Override
    public String getDraftDataType() {
        return DraftDataTypeEnum.LIST.getCode();
    }

    /**
     * 获取支持的步骤编码
     *
     * @return 步骤编码
     */
    @Override
    public String getStepCode() {
        return "create_team";
    }

    /**
     * 获取步骤名称（由子类实现）
     *
     * @return 步骤名称
     */
    @Override
    protected String getStepName() {
        return "组建评估团队";
    }

    @Override
    public StepDataResponse getStepData(EvaluatePlanTask planTask, String stepCode, Long processInstanceId) {
        // 1. 根据stepCode和processInstanceId查找对应的stepInstance
        ProcessStepInstance stepInstance = findStepInstanceByCode(stepCode, processInstanceId);

        // 2. 使用实体类构建响应
        StepDataResponse response = StepDataConverter.buildBaseStepDataResponse(planTask, stepInstance, stepCode, getEvaluateType());

        // 3. 添加特定的扩展数据
        // response.addStepData("assets", "组建评估团队");

        getSpecificStepData(planTask, stepInstance, processInstanceId, response);

        // 4. 转换为Map格式（保持向后兼容）
        return response;
    }

    /**
     * 保存步骤数据
     *
     * @param planTask          评估计划任务
     * @param stepCode          步骤编码
     * @param stepData          步骤数据
     * @param processInstanceId 流程实例ID
     * @return 保存是否成功
     */
    @Override
    public boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId) {
        log.info("保存步骤数据，任务ID: {}, 步骤: {}, 评估类型: {}", planTask.getId(), stepCode, planTask.getEvaluateType());

        // 保存特殊步骤数据
        saveSpecialStepData();
        return true;
    }

    /**
     * 处理具体步骤数据（由子类实现）
     *
     * @param planTask          评估计划任务
     * @param stepInstance      步骤实例
     * @param processInstanceId 流程实例ID
     * @param response          步骤数据响应对象，子类在此基础上添加扩展数据
     */
    @Override
    protected void getSpecificStepData(EvaluatePlanTask planTask, ProcessStepInstance stepInstance, Long processInstanceId, StepDataResponse response) {
        // 获取团队成员列表
        List<EvaluatePlanTeamMember> memberList = getMemberList(planTask);

        // 将成员列表添加到响应数据中
        response.addStepData("memberList", memberList);

        log.info("组建评估团队步骤数据处理完成，任务ID: {}, 成员数量: {}", planTask.getId(), memberList.size());
    }

    private boolean saveSpecialStepData() {
        log.info("保存特殊步骤数据");
        return true;
    }

    /**
     * 根据计划任务获取团队成员列表
     *
     * @param planTask 评估计划任务
     * @return 团队成员列表
     */
    private List<EvaluatePlanTeamMember> getMemberList(EvaluatePlanTask planTask) {
        List<EvaluatePlanTeamMember> memberList = new ArrayList<>();
        try {
            // 使用任务ID作为计划ID查询团队成员
            // 根据业务逻辑，这里使用任务ID作为planId进行查询
            Long planId = planTask.getId();

            log.info("开始查询计划ID为 {} 的团队成员", planId);

            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<EvaluatePlanTeamMember> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EvaluatePlanTeamMember::getPlanId, planId)
                    .orderByAsc(EvaluatePlanTeamMember::getGroupId)
                    .orderByAsc(EvaluatePlanTeamMember::getId);

            memberList = evaluatePlanTeamMemberService.list(queryWrapper);

            if (memberList == null || memberList.isEmpty()) {
                log.info("计划ID为 {} 的团队成员列表为空", planId);
                return memberList;
            }

            log.info("成功查询到计划ID为 {} 的团队成员 {} 人", planId, memberList.size());
            return memberList;

        } catch (Exception e) {
            log.error("查询计划ID为 {} 的团队成员时发生异常: {}", planTask.getId(), e.getMessage(), e);
            return memberList;
        }
    }

    /**
     * 重写列表类型暂存数据获取逻辑
     * 专门处理团队成员列表的暂存数据
     */
    @Override
    protected void getListDraftData(Long userId, EvaluatePlanTask planTask, String stepCode, StepDataResponse response) {
        Map<String, Object> draftData = new HashMap<>();
        draftData.put("memberList", getMemberList(planTask));
        response.addStepData(getDraftDataType(), draftData);
    }

    /**
     * 重写列表类型暂存数据保存逻辑
     * 专门处理团队成员列表的暂存数据
     */
    @Override
    protected boolean saveListDraftData(Long userId, Long planId, String stepCode, Object stepData) {
        String draftKey = planId + "_" + stepCode;
        log.info("开始保存团队成员列表暂存数据，用户ID: {}, 暂存键: {}", userId, draftKey);

        return true;
    }

    /**
     * 保存暂存数据
     *
     * @param userId   用户ID
     * @param planId   计划ID
     * @param stepCode 步骤编码
     * @param stepData 步骤数据
     * @return 保存结果
     */
    @Override
    public boolean saveDraftData(Long userId, Long planId, String stepCode, Object stepData) {
        log.warn("使用默认处理器保存暂存数据: {}, 计划ID: {}, 用户ID: {}", stepCode, planId, userId);

        return true;
    }
}
