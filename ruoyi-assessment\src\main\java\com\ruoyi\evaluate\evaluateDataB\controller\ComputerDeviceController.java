package com.ruoyi.evaluate.evaluateDataB.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.evaluate.evaluateDataB.domain.ComputerDevice;
import com.ruoyi.evaluate.evaluateDataB.service.IComputerDeviceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * B8 终端|现场设备Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/evaluateDataB/computerDevice")
@Api(value = "B8 终端|现场设备控制器", tags = {"B8 终端|现场设备管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ComputerDeviceController extends BaseController
{
    private final IComputerDeviceService computerDeviceService;

    /**
     * 查询B8 终端|现场设备列表
     */
    @ApiOperation("查询B8 终端|现场设备列表")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:computerDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(ComputerDevice computerDevice) {
        startPage();
        List<ComputerDevice> list = computerDeviceService.list(new QueryWrapper<ComputerDevice>(computerDevice));
        return getDataTable(list);
    }

    /**
     * 获取B8 终端|现场设备详细信息
     */
    @ApiOperation("获取B8 终端|现场设备详细信息")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:computerDevice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(computerDeviceService.getById(id));
    }

    /**
     * 新增B8 终端|现场设备
     */
    @ApiOperation("新增B8 终端|现场设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:computerDevice:add')")
    @Log(title = "B8 终端|现场设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ComputerDevice computerDevice) {
        return toAjax(computerDeviceService.save(computerDevice));
    }

    /**
     * 修改B8 终端|现场设备
     */
    @ApiOperation("修改B8 终端|现场设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:computerDevice:edit')")
    @Log(title = "B8 终端|现场设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ComputerDevice computerDevice) {
        return toAjax(computerDeviceService.updateById(computerDevice));
    }

    /**
     * 删除B8 终端|现场设备
     */
    @ApiOperation("删除B8 终端|现场设备")
    @PreAuthorize("@ss.hasPermi('evaluateDataB:computerDevice:remove')")
    @Log(title = "B8 终端|现场设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(computerDeviceService.removeByIds(Arrays.asList(ids)));
    }
}