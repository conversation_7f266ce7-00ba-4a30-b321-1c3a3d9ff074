package com.ruoyi.evaluate.evaluatePlan.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 检查清单对象 CheckList
 * 根据图片显示的检查项目结构设计
 *
 * <AUTHOR>
 * @date 2025/8/14 15:21
 * @description 检查清单实体类，包含被检查单位信息和评估单位信息的各项检查项目
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CheckList {
    private static final long serialVersionUID = 1L;

    /** 被检查单位信息 (占比16%) */
    private UnitInfo unitInfo;

    /** 评估单位 (占比20%) */
    private EvaluationUnit evaluationUnit;

    /**
     * 被检查单位信息内部类
     */
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class UnitInfo {
        /** AO基本信息 */
        private String aoBasicInfo;

        /** A1-数据处理者基本情况 */
        private String a1DataProcessorBasicInfo;

        /** A2-部门信息 */
        private String a2DepartmentInfo;

        /** A3-人员信息 */
        private String a3PersonnelInfo;

        /** A4-安全管理制度 */
        private String a4SecurityManagementSystem;

        /** A5-安全管理机构 */
        private String a5SecurityManagementOrganization;

        /** 权重占比 */
        private BigDecimal weightRatio = new BigDecimal("16");
    }

    /**
     * 评估单位内部类
     */
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class EvaluationUnit {
        /** 评估单位 */
        private String evaluationUnitName;

        /** 项目管理组 */
        private String projectManagementGroup;

        /** 技术支持组 */
        private String technicalSupportGroup;

        /** 质量管理组 */
        private String qualityManagementGroup;

        /** 配置管理组 */
        private String configurationManagementGroup;

        /** 权重占比 */
        private BigDecimal weightRatio = new BigDecimal("20");
    }

    /**
     * 获取所有检查项目列表
     */
    public List<CheckItem> getAllCheckItems() {
        List<CheckItem> items = new java.util.ArrayList<>();

        // 添加被检查单位信息项目
        if (unitInfo != null) {
            items.add(new CheckItem("AO", "AO基本信息", unitInfo.getAoBasicInfo()));
            items.add(new CheckItem("A1", "数据处理者基本情况", unitInfo.getA1DataProcessorBasicInfo()));
            items.add(new CheckItem("A2", "部门信息", unitInfo.getA2DepartmentInfo()));
            items.add(new CheckItem("A3", "人员信息", unitInfo.getA3PersonnelInfo()));
            items.add(new CheckItem("A4", "安全管理制度", unitInfo.getA4SecurityManagementSystem()));
            items.add(new CheckItem("A5", "安全管理机构", unitInfo.getA5SecurityManagementOrganization()));
        }

        // 添加评估单位项目
        if (evaluationUnit != null) {
            items.add(new CheckItem("EU", "评估单位", evaluationUnit.getEvaluationUnitName()));
            items.add(new CheckItem("PMG", "项目管理组", evaluationUnit.getProjectManagementGroup()));
            items.add(new CheckItem("TSG", "技术支持组", evaluationUnit.getTechnicalSupportGroup()));
            items.add(new CheckItem("QMG", "质量管理组", evaluationUnit.getQualityManagementGroup()));
            items.add(new CheckItem("CMG", "配置管理组", evaluationUnit.getConfigurationManagementGroup()));
        }

        return items;
    }

    /**
     * 检查项目内部类
     */
    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class CheckItem {
        /** 项目编码 */
        private String code;

        /** 项目名称 */
        private String name;

        /** 项目内容 */
        private String content;

        public CheckItem(String code, String name, String content) {
            this.code = code;
            this.name = name;
            this.content = content;
        }
    }
}
