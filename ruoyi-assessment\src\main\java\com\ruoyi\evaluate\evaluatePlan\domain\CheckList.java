package com.ruoyi.evaluate.evaluatePlan.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 检查清单对象 CheckList
 * 用于返回评估检查项目的详细信息
 *
 * <AUTHOR>
 * @date 2025/8/14 15:21
 * @description 检查清单实体类，包含各项检查项目的详细信息和评分
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
public class CheckList extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 评估计划ID */
    @Excel(name = "评估计划ID")
    @TableField(value = "plan_id")
    private Long planId;

    /** 所属单位ID */
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 检查项目编号 */
    @Excel(name = "检查项目编号")
    @TableField(value = "item_code")
    private String itemCode;

    /** 检查项目名称 */
    @Excel(name = "检查项目名称")
    @TableField(value = "item_name")
    private String itemName;

    /** 检查项目类型 (如：被检查单位信息、AO基本信息等) */
    @Excel(name = "检查项目类型")
    @TableField(value = "item_type")
    private String itemType;

    /** 检查项目分类 (如：管理类、技术类) */
    @Excel(name = "检查项目分类")
    @TableField(value = "item_category")
    private String itemCategory;

    /** 检查内容描述 */
    @Excel(name = "检查内容描述")
    @TableField(value = "check_content")
    private String checkContent;

    /** 检查要求 */
    @Excel(name = "检查要求")
    @TableField(value = "check_requirement")
    private String checkRequirement;

    /** 检查指导 */
    @Excel(name = "检查指导")
    @TableField(value = "check_guidance")
    private String checkGuidance;

    /** 是否必填项 1-是 0-否 */
    @Excel(name = "是否必填项")
    @TableField(value = "is_required")
    private Integer isRequired;

    /** 权重分值 */
    @Excel(name = "权重分值")
    @TableField(value = "weight_score")
    private BigDecimal weightScore;

    /** 满分分值 */
    @Excel(name = "满分分值")
    @TableField(value = "full_score")
    private BigDecimal fullScore;

    /** 实际得分 */
    @Excel(name = "实际得分")
    @TableField(value = "actual_score")
    private BigDecimal actualScore;

    /** 得分率 (实际得分/满分*100%) */
    @Excel(name = "得分率")
    @TableField(value = "score_rate")
    private BigDecimal scoreRate;

    /** 检查结果 1-符合 2-部分符合 3-不符合 4-不适用 */
    @Excel(name = "检查结果")
    @TableField(value = "check_result")
    private Integer checkResult;

    /** 检查结果描述 */
    @Excel(name = "检查结果描述")
    @TableField(value = "result_description")
    private String resultDescription;

    /** 问题描述 */
    @Excel(name = "问题描述")
    @TableField(value = "issue_description")
    private String issueDescription;

    /** 整改建议 */
    @Excel(name = "整改建议")
    @TableField(value = "improvement_advice")
    private String improvementAdvice;

    /** 检查状态 0-未检查 1-已检查 2-需复查 */
    @Excel(name = "检查状态")
    @TableField(value = "check_status")
    private Integer checkStatus;

    /** 检查人员 */
    @Excel(name = "检查人员")
    @TableField(value = "checker")
    private String checker;

    /** 检查时间 */
    @Excel(name = "检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "check_time")
    private java.util.Date checkTime;

    /** 排序值，越大越靠前 */
    @Excel(name = "排序值")
    @TableField(value = "sort")
    private Long sort;

    /** 状态，1-正常 0-禁用 */
    @Excel(name = "状态")
    @TableField(value = "status")
    private Integer status;

    /** 扩展字段1 */
    @TableField(value = "ext_field1")
    private String extField1;

    /** 扩展字段2 */
    @TableField(value = "ext_field2")
    private String extField2;

    /** 扩展字段3 */
    @TableField(value = "ext_field3")
    private String extField3;
}
