package com.ruoyi.evaluate.evaluateDataB.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateDataB.mapper.NetworkDeviceMapper;
import com.ruoyi.evaluate.evaluateDataB.domain.NetworkDevice;
import com.ruoyi.evaluate.evaluateDataB.service.INetworkDeviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * B5 网络设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class NetworkDeviceServiceImpl extends ServiceImpl<NetworkDeviceMapper, NetworkDevice> implements INetworkDeviceService {

}
