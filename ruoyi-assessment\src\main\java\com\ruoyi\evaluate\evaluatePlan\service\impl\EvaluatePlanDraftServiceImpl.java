package com.ruoyi.evaluate.evaluatePlan.service.impl;

import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.service.IDraftStorageService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.SaveStepRequest;
import com.ruoyi.evaluate.evaluatePlan.dto.DraftStepRequest;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanDraftService;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanTaskService;
import com.ruoyi.evaluate.evaluatePlan.service.processor.IStepDataProcessor;
import com.ruoyi.evaluate.evaluatePlan.service.processor.StepDataProcessorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 评估计划暂存服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluatePlanDraftServiceImpl implements IEvaluatePlanDraftService {

    private final IDraftStorageService draftStorageService;
    private final IEvaluatePlanTaskService evaluatePlanTaskService;
    private final StepDataProcessorFactory stepDataProcessorFactory;

    @Override
    public boolean saveDraft(Long userId, SaveStepRequest request) {
        try {
            // 参数校验
            validateSaveRequest(request);

            // 获取评估计划任务以获得评估类型
            EvaluatePlanTask planTask = getEvaluatePlanTask(request.getPlanId());
            String evaluateType = planTask.getEvaluateType();
            if (StringUtils.isEmpty(evaluateType)) {
                log.warn("评估计划任务的评估类型为空，任务ID: {}", request.getPlanId());
                throw new ServiceException("评估计划任务的评估类型不能为空");
            }

            // 获取步骤数据处理器
            IStepDataProcessor processor = stepDataProcessorFactory.getProcessor(evaluateType, request.getStepCode());

            log.debug("保存暂存数据，用户ID: {}, 步骤: {}, 数据类型: {}", userId, request.getStepCode(), processor.getDraftDataType());

            // 委托给处理器处理暂存数据保存
            return processor.saveDraftData(userId, request.getPlanId(), request.getStepCode(), request.getStepData());

        } catch (Exception e) {
            log.error("暂存数据异常，用户ID: {}, 请求: {}, 错误: {}", userId, request, e.getMessage(), e);
            throw new ServiceException("暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public StepDataResponse getDraft(Long userId, DraftStepRequest request) {
        try {
            // 参数校验
            validateStepRequest(request);

            // 获取评估计划任务以获得评估类型
            EvaluatePlanTask planTask = getEvaluatePlanTask(request.getPlanId());
            String evaluateType = planTask.getEvaluateType();
            if (StringUtils.isEmpty(evaluateType)) {
                log.warn("评估计划任务的评估类型为空，任务ID: {}", request.getPlanId());
                throw new ServiceException("评估计划任务的评估类型不能为空");
            }

            // 获取步骤数据处理器
            IStepDataProcessor processor = stepDataProcessorFactory.getProcessor(evaluateType, request.getStepCode());

            log.debug("获取暂存数据，用户ID: {}, 步骤: {}, 数据类型: {}", userId, request.getStepCode(), processor.getDraftDataType());

            // 委托给处理器处理暂存数据获取（参数与getStepData保持一致）
            Long processInstanceId = null; // 暂存数据获取通常不需要流程实例ID
            return processor.getDraftData(planTask, request.getStepCode(), processInstanceId);

        } catch (Exception e) {
            log.error("获取暂存数据异常，用户ID: {}, 请求: {}, 错误: {}", userId, request, e.getMessage(), e);
            throw new ServiceException("获取暂存数据失败：" + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 校验保存请求参数
     */
    private void validateSaveRequest(SaveStepRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (!request.isValid()) {
            throw new IllegalArgumentException("请求参数无效：" + request.getDescription());
        }
    }

    /**
     * 校验步骤请求参数
     */
    private void validateStepRequest(DraftStepRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (!request.isValid()) {
            throw new IllegalArgumentException("请求参数无效：" + request.getDescription());
        }
    }

    /**
     * 获取评估计划任务
     */
    private EvaluatePlanTask getEvaluatePlanTask(Long planId) {
        EvaluatePlanTask planTask = evaluatePlanTaskService.getById(planId);
        if (planTask == null) {
            throw new ServiceException("评估计划任务不存在，ID: " + planId);
        }
        return planTask;
    }
}
