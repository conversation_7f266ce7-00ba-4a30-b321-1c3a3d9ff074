package com.ruoyi.evaluate.evaluatePlan.domain;

import java.math.BigDecimal;
import java.util.List;

/**
 * 检查分类对象
 *
 * <AUTHOR>
 * @date 2025/8/14
 * @description 检查分类实体类，包含分类信息和统计功能
 */
public class CheckCategory {
    /** 分类名称 */
    private String categoryName;
    
    /** 检查项目列表 */
    private List<CheckList.CheckItem> checkItems;
    
    /**
     * 获取通过比例（分数表示）
     * @return 通过比例，格式为 "通过数/总数"
     */
    public String getPassRatio() {
        if (checkItems == null || checkItems.isEmpty()) {
            return "0/0";
        }
        
        int totalCount = checkItems.size();
        int passedCount = 0;
        
        for (CheckList.CheckItem item : checkItems) {
            if (Boolean.TRUE.equals(item.getPassed())) {
                passedCount++;
            }
        }
        
        return passedCount + "/" + totalCount;
    }
    
    /**
     * 获取通过率（百分比）
     * @return 通过率百分比（整数）
     */
    public BigDecimal getPassPercentage() {
        if (checkItems == null || checkItems.isEmpty()) {
            return BigDecimal.ZERO;
        }

        int totalCount = checkItems.size();
        int passedCount = 0;

        for (CheckList.CheckItem item : checkItems) {
            if (Boolean.TRUE.equals(item.getPassed())) {
                passedCount++;
            }
        }

        return new BigDecimal(passedCount)
                .divide(new BigDecimal(totalCount), 0, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));
    }
    
    /**
     * 获取各状态统计
     * @return 状态统计信息
     */
    public String getStatusSummary() {
        if (checkItems == null || checkItems.isEmpty()) {
            return "通过:0 不通过:0 待检查:0";
        }
        
        int passedCount = 0;
        int failedCount = 0;
        int pendingCount = 0;
        
        for (CheckList.CheckItem item : checkItems) {
            if (Boolean.TRUE.equals(item.getPassed())) {
                passedCount++;
            } else if (Boolean.FALSE.equals(item.getPassed())) {
                failedCount++;
            } else {
                pendingCount++;
            }
        }
        
        return String.format("通过:%d 不通过:%d 待检查:%d", passedCount, failedCount, pendingCount);
    }
    
    // Getters and Setters
    public String getCategoryName() {
        return categoryName;
    }
    
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    
    public List<CheckList.CheckItem> getCheckItems() {
        return checkItems;
    }
    
    public void setCheckItems(List<CheckList.CheckItem> checkItems) {
        this.checkItems = checkItems;
    }
}
