package com.ruoyi.evaluate.evaluateDataB.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.MyBaseEntity;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * B7 服务器|存储设备对象 dsa_server_storage
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Accessors(chain = true)
@TableName("dsa_server_storage")
public class ServerStorage extends MyBaseEntity {
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(value = "id" , type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 所属单位ID */
    @Excel(name = "所属单位ID")
    @TableField(value = "org_id")
    private Long orgId;

    /** 评估计划ID */
    @Excel(name = "评估计划ID")
    @TableField(value = "plan_id")
    private Long planId;

    /** 系统ID */
    @Excel(name = "系统ID")
    @TableField(value = "system_id")
    private Long systemId;

    /** 数据项ID */
    @Excel(name = "数据项ID")
    @TableField(value = "data_id")
    private Long dataId;

    /** 设备名称 */
    @Excel(name = "设备名称")
    @TableField(value = "name")
    private String name;

    /** 信息系统名称 */
    @Excel(name = "信息系统名称")
    @TableField(value = "system_name")
    private String systemName;

    /** 是否虚拟设备,是 否 */
    @Excel(name = "是否虚拟设备,是 否")
    @TableField(value = "is_virtual")
    private String isVirtual;

    /** 操作系统及版本 */
    @Excel(name = "操作系统及版本")
    @TableField(value = "os_version")
    private String osVersion;

    /** 数据库管理系统及版本 */
    @Excel(name = "数据库管理系统及版本")
    @TableField(value = "db_version")
    private String dbVersion;

    /** 中间件及版本 */
    @Excel(name = "中间件及版本")
    @TableField(value = "middware_version")
    private String middwareVersion;



    /** uuid */
    @Excel(name = "uuid")
    @TableField(value = "uuid")
    private String uuid;


}