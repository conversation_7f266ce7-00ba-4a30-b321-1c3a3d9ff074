package com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurityPlan;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.service.processor.AbstractStepDataProcessor;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import com.ruoyi.process.domain.ProcessStepInstance;
import org.springframework.stereotype.Component;

/**
 * 签字确认
 *
 * <AUTHOR>
 * @date 2025/7/30 17:38
 */
@Component
public class SignProcessor extends AbstractStepDataProcessor {

    @Override
    public String getEvaluateType() {
        return "data_security_plan";
    }

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    @Override
    public String getDraftDataType() {
        return null;
    }

    /**
     * 获取支持的步骤编码
     *
     * @return 步骤编码
     */
    @Override
    public String getStepCode() {
        return "sign";
    }

    /**
     * 获取步骤名称（由子类实现）
     *
     * @return 步骤名称
     */
    @Override
    protected String getStepName() {
        return "签字确认";
    }

    @Override
    public StepDataResponse getStepData(EvaluatePlanTask planTask, String stepCode, Long processInstanceId) {
        // 1. 根据stepCode和processInstanceId查找对应的stepInstance
        ProcessStepInstance stepInstance = findStepInstanceByCode(stepCode, processInstanceId);

        // 2. 使用实体类构建响应
        StepDataResponse response = StepDataConverter.buildBaseStepDataResponse(planTask, stepInstance, stepCode, getEvaluateType());

        // 3. 添加特定的扩展数据
        response.addStepData("assets", "签字确认");

        // 4. 转换为Map格式（保持向后兼容）
        return response;
    }

    /**
     * 保存步骤数据
     *
     * @param planTask          评估计划任务
     * @param stepCode          步骤编码
     * @param stepData          步骤数据
     * @param processInstanceId 流程实例ID
     * @return 保存是否成功
     */
    @Override
    public boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId) {
        return false;
    }

    /**
     * 处理具体步骤数据（由子类实现）
     *
     * @param planTask          评估计划任务
     * @param stepInstance      步骤实例
     * @param processInstanceId 流程实例ID
     * @param response          步骤数据响应对象，子类在此基础上添加扩展数据
     */
    @Override
    protected void getSpecificStepData(EvaluatePlanTask planTask, ProcessStepInstance stepInstance, Long processInstanceId, StepDataResponse response) {

    }
}
