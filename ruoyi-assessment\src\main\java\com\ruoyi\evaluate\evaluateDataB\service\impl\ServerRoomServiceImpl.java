package com.ruoyi.evaluate.evaluateDataB.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateDataB.mapper.ServerRoomMapper;
import com.ruoyi.evaluate.evaluateDataB.domain.ServerRoom;
import com.ruoyi.evaluate.evaluateDataB.service.IServerRoomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * B4 物理机房Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class ServerRoomServiceImpl extends ServiceImpl<ServerRoomMapper, ServerRoom> implements IServerRoomService {

}
