package com.ruoyi.evaluate.evaluateDataB.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.evaluate.evaluateDataB.mapper.SystemInfoMapper;
import com.ruoyi.evaluate.evaluateDataB.domain.SystemInfo;
import com.ruoyi.evaluate.evaluateDataB.service.ISystemInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * B3 信息系统情况Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class SystemInfoServiceImpl extends ServiceImpl<SystemInfoMapper, SystemInfo> implements ISystemInfoService {

}
